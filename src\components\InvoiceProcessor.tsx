
import React, { useState, useCallback } from 'react';
import { Zap, Brain, FileText, DollarSign, Calendar, Building, ShoppingCart, Download, Save } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  dueDate: string;
  vendor: string;
  amount: string;
  tax: string;
  description: string;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
}

interface InvoiceProcessorProps {
  onProcessComplete: () => void;
}

const InvoiceProcessor: React.FC<InvoiceProcessorProps> = ({ onProcessComplete }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [selectedProvider, setSelectedProvider] = useState<string>('openai');
  const [extractedData, setExtractedData] = useState<InvoiceData | null>(null);
  const [confidence, setConfidence] = useState(0);
  const { toast } = useToast();

  const processingSteps = [
    'Analyzing image quality',
    'Extracting text with OCR',
    'Processing with AI',
    'Validating extracted data',
    'Formatting results'
  ];

  const mockProcessInvoice = useCallback(async () => {
    setIsProcessing(true);
    setProcessingStep(0);

    // Simulate processing steps
    for (let i = 0; i < processingSteps.length; i++) {
      setProcessingStep(i);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Mock extracted data
    const mockData: InvoiceData = {
      invoiceNumber: 'INV-2024-001',
      date: '2024-01-15',
      dueDate: '2024-02-15',
      vendor: 'Tech Solutions Inc.',
      amount: '1,250.00',
      tax: '125.00',
      description: 'Software licensing and support services',
      items: [
        { description: 'Software License', quantity: 1, unitPrice: 1000.00, total: 1000.00 },
        { description: 'Support Services', quantity: 1, unitPrice: 250.00, total: 250.00 }
      ]
    };

    setExtractedData(mockData);
    setConfidence(96);
    setIsProcessing(false);

    toast({
      title: "Processing Complete",
      description: "Invoice data extracted successfully with 96% confidence.",
    });
  }, [toast]);

  const handleSaveToSheets = useCallback(async () => {
    if (!extractedData) return;

    try {
      // Simulate Google Sheets API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Saved to Google Sheets",
        description: "Invoice data has been successfully saved to your spreadsheet.",
      });
      
      onProcessComplete();
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save to Google Sheets. Please check your configuration.",
        variant: "destructive"
      });
    }
  }, [extractedData, onProcessComplete, toast]);

  const updateField = (field: keyof InvoiceData, value: string) => {
    if (!extractedData) return;
    setExtractedData({ ...extractedData, [field]: value });
  };

  return (
    <div className="space-y-6">
      {/* AI Provider Selection */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2 text-invoice-blue-600" />
            AI Processing Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="provider">AI Provider</Label>
              <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                <SelectTrigger>
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI GPT-4 Vision</SelectItem>
                  <SelectItem value="claude">Anthropic Claude 3</SelectItem>
                  <SelectItem value="google">Google Gemini Pro</SelectItem>
                  <SelectItem value="local">Local Model (Offline)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={mockProcessInvoice} 
                disabled={isProcessing}
                className="gradient-primary w-full"
              >
                <Zap className="w-4 h-4 mr-2" />
                {isProcessing ? 'Processing...' : 'Process Invoice'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Processing Status */}
      {isProcessing && (
        <Card className="invoice-shadow animate-fade-in">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Processing Invoice</span>
                <Badge variant="secondary">{selectedProvider.toUpperCase()}</Badge>
              </div>
              <Progress value={(processingStep + 1) / processingSteps.length * 100} className="h-2" />
              <p className="text-sm text-muted-foreground flex items-center">
                <div className="w-2 h-2 bg-invoice-blue-500 rounded-full mr-2 animate-pulse"></div>
                {processingSteps[processingStep]}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Extracted Data */}
      {extractedData && (
        <div className="space-y-6 animate-fade-in">
          <Card className="invoice-shadow">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-invoice-teal-600" />
                  Extracted Invoice Data
                </div>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {confidence}% Confidence
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="invoiceNumber">Invoice Number</Label>
                  <Input
                    id="invoiceNumber"
                    value={extractedData.invoiceNumber}
                    onChange={(e) => updateField('invoiceNumber', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="date">Invoice Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={extractedData.date}
                    onChange={(e) => updateField('date', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={extractedData.dueDate}
                    onChange={(e) => updateField('dueDate', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vendor">Vendor</Label>
                  <Input
                    id="vendor"
                    value={extractedData.vendor}
                    onChange={(e) => updateField('vendor', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={extractedData.description}
                    onChange={(e) => updateField('description', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>

              {/* Financial Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Total Amount</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="amount"
                      value={extractedData.amount}
                      onChange={(e) => updateField('amount', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="tax">Tax Amount</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="tax"
                      value={extractedData.tax}
                      onChange={(e) => updateField('tax', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Line Items */}
              <div>
                <Label className="text-base font-semibold flex items-center mb-4">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Line Items
                </Label>
                <div className="border rounded-lg overflow-hidden">
                  <div className="bg-muted p-3 grid grid-cols-12 gap-2 text-sm font-medium">
                    <div className="col-span-5">Description</div>
                    <div className="col-span-2">Qty</div>
                    <div className="col-span-2">Unit Price</div>
                    <div className="col-span-3">Total</div>
                  </div>
                  {extractedData.items.map((item, index) => (
                    <div key={index} className="p-3 grid grid-cols-12 gap-2 text-sm border-t">
                      <div className="col-span-5">{item.description}</div>
                      <div className="col-span-2">{item.quantity}</div>
                      <div className="col-span-2">${item.unitPrice.toFixed(2)}</div>
                      <div className="col-span-3 font-medium">${item.total.toFixed(2)}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-4">
                <Button onClick={handleSaveToSheets} className="gradient-primary flex-1">
                  <Save className="w-4 h-4 mr-2" />
                  Save to Google Sheets
                </Button>
                <Button variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default InvoiceProcessor;
