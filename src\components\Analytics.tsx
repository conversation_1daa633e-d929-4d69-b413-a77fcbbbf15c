
import React from 'react';
import { Bar<PERSON>hart3, TrendingUp, DollarSign, Clock, Users, Calendar } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface AnalyticsProps {
  totalProcessed: number;
}

const Analytics: React.FC<AnalyticsProps> = ({ totalProcessed }) => {
  const monthlyData = [
    { month: 'Jan', invoices: 45, amount: 12500 },
    { month: 'Feb', invoices: 52, amount: 15200 },
    { month: 'Mar', invoices: 48, amount: 13800 },
    { month: 'Apr', invoices: 61, amount: 18400 },
    { month: 'May', invoices: 55, amount: 16700 },
    { month: 'Jun', invoices: 58, amount: 17900 }
  ];

  const vendorData = [
    { vendor: 'Tech Solutions', count: 15, amount: 8500, color: '#3b82f6' },
    { vendor: 'Office Supplies', count: 12, amount: 3200, color: '#14b8a6' },
    { vendor: 'Marketing Co', count: 8, amount: 4800, color: '#f59e0b' },
    { vendor: 'Cloud Services', count: 10, amount: 2400, color: '#8b5cf6' },
    { vendor: 'Others', count: 20, amount: 6100, color: '#6b7280' }
  ];

  const accuracyData = [
    { range: '95-100%', count: 42, color: '#10b981' },
    { range: '90-94%', count: 18, color: '#f59e0b' },
    { range: '85-89%', count: 5, color: '#ef4444' }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="invoice-shadow hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Processed</CardTitle>
            <BarChart3 className="h-4 w-4 text-invoice-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProcessed}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="invoice-shadow hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$17,900</div>
            <p className="text-xs text-muted-foreground">
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="invoice-shadow hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-invoice-teal-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.3s</div>
            <p className="text-xs text-muted-foreground">
              -0.5s from last month
            </p>
          </CardContent>
        </Card>

        <Card className="invoice-shadow hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accuracy Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">96.8%</div>
            <p className="text-xs text-muted-foreground">
              +1.2% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <Card className="invoice-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-invoice-blue-600" />
              Monthly Processing Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="invoices" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vendor Distribution */}
        <Card className="invoice-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2 text-invoice-teal-600" />
              Top Vendors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={vendorData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="vendor" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#14b8a6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Accuracy Distribution */}
        <Card className="invoice-shadow">
          <CardHeader>
            <CardTitle>Accuracy Distribution</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {accuracyData.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{item.range}</span>
                  <span className="font-medium">{item.count} invoices</span>
                </div>
                <Progress 
                  value={(item.count / 65) * 100} 
                  className="h-2"
                />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Processing Summary */}
        <Card className="invoice-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-invoice-blue-600" />
              This Month Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gradient-to-br from-invoice-blue-50 to-invoice-teal-50 rounded-lg">
                <div className="text-2xl font-bold text-invoice-blue-600">58</div>
                <div className="text-sm text-muted-foreground">Invoices Processed</div>
              </div>
              <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">$17.9K</div>
                <div className="text-sm text-muted-foreground">Total Value</div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing Goal</span>
                <span>58/75</span>
              </div>
              <Progress value={77} className="h-2" />
              <p className="text-xs text-muted-foreground">
                77% of monthly goal achieved
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Analytics;
