
import React, { useState } from 'react';
import { Setting<PERSON>, <PERSON>, <PERSON>, Brain, Bell, Download, Upload, Shield } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

const SettingsPanel: React.FC = () => {
  const [apiKeys, setApiKeys] = useState({
    openai: '',
    claude: '',
    gemini: '',
    googleSheets: ''
  });
  const [notifications, setNotifications] = useState({
    processComplete: true,
    errors: true,
    dailyReport: false
  });
  const [autoUpload, setAutoUpload] = useState(true);
  const [defaultProvider, setDefaultProvider] = useState('deepseek');
  const { toast } = useToast();

  const handleSaveSettings = () => {
    toast({
      title: "Settings Saved",
      description: "Your configuration has been saved successfully.",
    });
  };

  const handleTestConnection = (service: string) => {
    toast({
      title: "Testing Connection",
      description: `Testing connection to ${service}...`,
    });
    
    // Simulate API test
    setTimeout(() => {
      toast({
        title: "Connection Successful",
        description: `Successfully connected to ${service}.`,
      });
    }, 2000);
  };

  const updateApiKey = (service: keyof typeof apiKeys, value: string) => {
    setApiKeys(prev => ({ ...prev, [service]: value }));
  };

  return (
    <div className="space-y-6">
      {/* API Configuration */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Key className="w-5 h-5 mr-2 text-invoice-blue-600" />
            API Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* OpenAI */}
          <div className="space-y-2">
            <Label htmlFor="openai-key" className="flex items-center justify-between">
              <span>OpenAI API Key</span>
              <Badge variant="outline">GPT-4 Vision</Badge>
            </Label>
            <div className="flex gap-2">
              <Input
                id="openai-key"
                type="password"
                placeholder="sk-..."
                value={apiKeys.openai}
                onChange={(e) => updateApiKey('openai', e.target.value)}
              />
              <Button 
                variant="outline" 
                onClick={() => handleTestConnection('OpenAI')}
                disabled={!apiKeys.openai}
              >
                Test
              </Button>
            </div>
          </div>

          {/* Claude */}
          <div className="space-y-2">
            <Label htmlFor="claude-key" className="flex items-center justify-between">
              <span>Anthropic API Key</span>
              <Badge variant="outline">Claude 3</Badge>
            </Label>
            <div className="flex gap-2">
              <Input
                id="claude-key"
                type="password"
                placeholder="sk-ant-..."
                value={apiKeys.claude}
                onChange={(e) => updateApiKey('claude', e.target.value)}
              />
              <Button 
                variant="outline" 
                onClick={() => handleTestConnection('Claude')}
                disabled={!apiKeys.claude}
              >
                Test
              </Button>
            </div>
          </div>

          {/* Google Gemini */}
          <div className="space-y-2">
            <Label htmlFor="gemini-key" className="flex items-center justify-between">
              <span>Google AI API Key</span>
              <Badge variant="outline">Gemini Pro</Badge>
            </Label>
            <div className="flex gap-2">
              <Input
                id="gemini-key"
                type="password"
                placeholder="AI..."
                value={apiKeys.gemini}
                onChange={(e) => updateApiKey('gemini', e.target.value)}
              />
              <Button 
                variant="outline" 
                onClick={() => handleTestConnection('Google AI')}
                disabled={!apiKeys.gemini}
              >
                Test
              </Button>
            </div>
          </div>

          <div className="pt-4">
            <Label htmlFor="default-provider">Default AI Provider</Label>
            <Select value={defaultProvider} onValueChange={setDefaultProvider}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
                <SelectItem value="openai">OpenAI GPT-4 Vision</SelectItem>
                <SelectItem value="claude">Anthropic Claude 3</SelectItem>
                <SelectItem value="gemini">Google Gemini Pro</SelectItem>
                <SelectItem value="local">Local Model</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Google Sheets Integration */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Link className="w-5 h-5 mr-2 text-invoice-teal-600" />
            Google Sheets Integration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="sheets-id">Spreadsheet ID</Label>
            <Input
              id="sheets-id"
              placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
              value={apiKeys.googleSheets}
              onChange={(e) => updateApiKey('googleSheets', e.target.value)}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch id="auto-upload" checked={autoUpload} onCheckedChange={setAutoUpload} />
            <Label htmlFor="auto-upload">Automatically upload processed invoices</Label>
          </div>

          <Button 
            onClick={() => handleTestConnection('Google Sheets')} 
            className="w-full"
            variant="outline"
          >
            <Link className="w-4 h-4 mr-2" />
            Connect to Google Sheets
          </Button>
        </CardContent>
      </Card>

      {/* Processing Settings */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            Processing Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="confidence-threshold">Minimum Confidence Threshold</Label>
              <Select defaultValue="90">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="85">85%</SelectItem>
                  <SelectItem value="90">90%</SelectItem>
                  <SelectItem value="95">95%</SelectItem>
                  <SelectItem value="98">98%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="retry-attempts">Max Retry Attempts</Label>
              <Select defaultValue="3">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="2">2</SelectItem>
                  <SelectItem value="3">3</SelectItem>
                  <SelectItem value="5">5</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="custom-prompt">Custom Processing Prompt</Label>
            <Textarea
              id="custom-prompt"
              placeholder="Enter custom instructions for AI processing..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notifications */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="w-5 h-5 mr-2 text-yellow-600" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="notify-complete">Processing Complete</Label>
              <p className="text-sm text-muted-foreground">Get notified when invoice processing is complete</p>
            </div>
            <Switch 
              id="notify-complete" 
              checked={notifications.processComplete}
              onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, processComplete: checked }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="notify-errors">Error Notifications</Label>
              <p className="text-sm text-muted-foreground">Get notified when processing errors occur</p>
            </div>
            <Switch 
              id="notify-errors" 
              checked={notifications.errors}
              onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, errors: checked }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="notify-daily">Daily Reports</Label>
              <p className="text-sm text-muted-foreground">Receive daily processing summary reports</p>
            </div>
            <Switch 
              id="notify-daily" 
              checked={notifications.dailyReport}
              onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, dailyReport: checked }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card className="invoice-shadow">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2 text-green-600" />
            Data Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Download className="w-6 h-6 mb-2" />
              Export All Data
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Upload className="w-6 h-6 mb-2" />
              Import Data
            </Button>
          </div>
          
          <div className="pt-4 border-t">
            <Button variant="destructive" className="w-full">
              Clear All Processing History
            </Button>
            <p className="text-xs text-muted-foreground mt-2">
              This action cannot be undone. All processing history will be permanently deleted.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} className="gradient-primary">
          <Settings className="w-4 h-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  );
};

export default SettingsPanel;
