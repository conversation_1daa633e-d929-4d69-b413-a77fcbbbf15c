
import React, { useState } from 'react';
import { <PERSON>, FileText, Settings, BarChart3, Upload, Zap } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CameraCapture from '@/components/CameraCapture';
import InvoiceProcessor from '@/components/InvoiceProcessor';
import SettingsPanel from '@/components/SettingsPanel';
import Analytics from '@/components/Analytics';
import ProcessingHistory from '@/components/ProcessingHistory';

type ActiveTab = 'home' | 'camera' | 'processor' | 'history' | 'analytics' | 'settings';

const Index = () => {
  const [activeTab, setActiveTab] = useState<ActiveTab>('home');
  const [processedInvoices, setProcessedInvoices] = useState(0);

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'camera':
        return <CameraCapture onCapture={(image) => {
          console.log('Image captured:', image);
          setActiveTab('processor');
        }} />;
      case 'processor':
        return <InvoiceProcessor onProcessComplete={() => {
          setProcessedInvoices(prev => prev + 1);
          setActiveTab('history');
        }} />;
      case 'history':
        return <ProcessingHistory />;
      case 'analytics':
        return <Analytics totalProcessed={processedInvoices} />;
      case 'settings':
        return <SettingsPanel />;
      default:
        return (
          <div className="space-y-8">
            {/* Hero Section */}
            <div className="text-center py-12 gradient-primary rounded-2xl text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 animate-pulse-glow">
                  <Zap className="w-10 h-10" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-sm">Smart Invoice Sheets</h1>
                <p className="text-xl md:text-2xl text-white mb-8 max-w-2xl mx-auto leading-relaxed font-medium drop-shadow-sm">
                  AI-powered invoice processing that automatically extracts data and stores it in Google Sheets
                </p>
                <div className="flex gap-4 justify-center flex-wrap">
                  <Button
                    onClick={() => setActiveTab('camera')}
                    size="lg"
                    className="bg-trust-orange-500 text-white hover:bg-trust-orange-600 shadow-lg font-semibold text-base"
                  >
                    <Camera className="w-5 h-5 mr-2" />
                    Capture Invoice
                  </Button>
                  <Button
                    onClick={() => setActiveTab('processor')}
                    size="lg"
                    variant="outline"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 font-semibold text-base"
                  >
                    <Upload className="w-5 h-5 mr-2" />
                    Upload File
                  </Button>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="invoice-shadow hover:shadow-lg transition-shadow bg-gradient-to-br from-trust-blue-50 to-white border-trust-blue-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-gray-700">Total Processed</CardTitle>
                  <FileText className="h-4 w-4 text-trust-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-trust-blue-600">{processedInvoices}</div>
                  <p className="text-sm font-medium mt-1 text-gray-600">Invoices this month</p>
                </CardContent>
              </Card>

              <Card className="invoice-shadow hover:shadow-lg transition-shadow bg-gradient-to-br from-trust-green-50 to-white border-trust-green-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-gray-700">Accuracy Rate</CardTitle>
                  <BarChart3 className="h-4 w-4 text-trust-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-trust-green-600">98.5%</div>
                  <p className="text-sm font-medium mt-1 text-gray-600">AI extraction accuracy</p>
                </CardContent>
              </Card>

              <Card className="invoice-shadow hover:shadow-lg transition-shadow bg-gradient-to-br from-trust-orange-50 to-white border-trust-orange-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-gray-700">Time Saved</CardTitle>
                  <Zap className="h-4 w-4 text-trust-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-trust-orange-600">2.4h</div>
                  <p className="text-sm font-medium mt-1 text-gray-600">Per day average</p>
                </CardContent>
              </Card>
            </div>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="p-6 invoice-shadow hover:shadow-lg transition-all hover:scale-[1.02] bg-gradient-to-br from-trust-blue-50 to-white border-trust-blue-100">
                <div className="flex items-start space-x-4">
                  <div className="bg-trust-blue-100 p-3 rounded-lg">
                    <Camera className="w-6 h-6 text-trust-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold mb-3 text-gray-800">Smart Capture</h3>
                    <p className="text-base leading-relaxed mb-4 font-medium text-gray-600">
                      Use your camera to capture invoices with automatic edge detection and image optimization.
                    </p>
                    <Button onClick={() => setActiveTab('camera')} className="bg-trust-blue-500 hover:bg-trust-blue-600 text-white font-semibold">
                      Start Capturing
                    </Button>
                  </div>
                </div>
              </Card>

              <Card className="p-6 invoice-shadow hover:shadow-lg transition-all hover:scale-[1.02] bg-gradient-to-br from-trust-green-50 to-white border-trust-green-100">
                <div className="flex items-start space-x-4">
                  <div className="bg-trust-green-100 p-3 rounded-lg">
                    <Zap className="w-6 h-6 text-trust-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold mb-3 text-gray-800">AI Processing</h3>
                    <p className="text-base leading-relaxed mb-4 font-medium text-gray-600">
                      Advanced AI extracts key information like amounts, dates, vendor details, and line items.
                    </p>
                    <Button onClick={() => setActiveTab('processor')} variant="outline" className="border-trust-green-500 text-trust-green-600 hover:bg-trust-green-50 font-semibold">
                      View Processor
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-trust-blue-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center mr-3">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-800">Smart Invoice Sheets</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant={activeTab === 'home' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('home')}
                size="sm"
              >
                Home
              </Button>
              <Button
                variant={activeTab === 'camera' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('camera')}
                size="sm"
              >
                <Camera className="w-4 h-4 mr-2" />
                Camera
              </Button>
              <Button
                variant={activeTab === 'history' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('history')}
                size="sm"
              >
                History
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('analytics')}
                size="sm"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Analytics
              </Button>
              <Button
                variant={activeTab === 'settings' ? 'default' : 'ghost'}
                onClick={() => setActiveTab('settings')}
                size="sm"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-fade-in">
          {renderActiveComponent()}
        </div>
      </main>
    </div>
  );
};

export default Index;
