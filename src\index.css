
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global text readability overrides */
body, p, span, div, h1, h2, h3, h4, h5, h6 {
  color: rgb(15 23 42) !important;
}

.text-muted-foreground,
[class*="muted"] {
  color: rgb(30 41 59) !important;
  font-weight: 500 !important;
}

/* Invoice App Design System */
@layer base {
  :root {
    /* Trust & Growth Theme */
    --background: 255 255 255;
    --foreground: 31 41 55;

    --card: 255 255 255;
    --card-foreground: 31 41 55;

    --popover: 255 255 255;
    --popover-foreground: 31 41 55;

    --primary: 37 99 235;
    --primary-foreground: 255 255 255;

    --secondary: 249 250 251;
    --secondary-foreground: 31 41 55;

    --muted: 249 250 251;
    --muted-foreground: 75 85 99;

    --accent: 245 158 11;
    --accent-foreground: 255 255 255;

    --success: 5 150 105;
    --success-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 37 99 235;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 31 41 55;
    --sidebar-primary: 37 99 235;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 249 250 251;
    --sidebar-accent-foreground: 31 41 55;
    --sidebar-border: 229 231 235;
    --sidebar-ring: 37 99 235;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;

    --card: 30 41 59;
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 51 65 85;
    --secondary-foreground: 226 232 240;

    --muted: 51 65 85;
    --muted-foreground: 203 213 225;

    --accent: 59 130 246;
    --accent-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 59 130 246;

    --sidebar-background: 30 41 59;
    --sidebar-foreground: 226 232 240;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 51 65 85;
    --sidebar-accent-foreground: 226 232 240;
    --sidebar-border: 51 65 85;
    --sidebar-ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-size: 16px;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground font-semibold;
    line-height: 1.3;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  p {
    @apply text-foreground;
    line-height: 1.7;
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground)) !important;
    font-weight: 500;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1.4;
    font-weight: 500;
  }

  ::selection {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}
 
@layer components {
  .gradient-primary {
    @apply bg-gradient-to-br from-trust-blue-500 to-trust-green-500;
  }

  .gradient-secondary {
    @apply bg-gradient-to-br from-trust-blue-50 to-trust-green-50;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-sm border border-white/20;
  }

  .invoice-shadow {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
  }

  /* Enhanced readability utilities */
  .text-readable {
    color: rgb(15 23 42) !important;
    font-weight: 600 !important;
    letter-spacing: 0.01em;
  }

  .text-readable-muted {
    color: rgb(30 41 59) !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em;
  }

  .card-title-readable {
    color: rgb(15 23 42) !important;
    font-weight: 700 !important;
    font-size: 1.125rem;
    line-height: 1.4;
    letter-spacing: -0.01em;
  }

  .button-text-readable {
    font-weight: 600 !important;
    letter-spacing: 0.01em;
  }

  /* Improved contrast for stats and metrics */
  .stat-number {
    color: rgb(15 23 42) !important;
    font-weight: 800 !important;
    letter-spacing: -0.02em;
  }

  .stat-label {
    color: rgb(30 41 59) !important;
    font-weight: 600 !important;
    font-size: 0.875rem;
    letter-spacing: 0.01em;
  }

  /* Force better text contrast globally */
  .text-muted-foreground {
    color: rgb(30 41 59) !important;
    font-weight: 500 !important;
  }

  h1, h2, h3, h4, h5, h6 {
    color: rgb(15 23 42) !important;
    font-weight: 700 !important;
  }

  p {
    color: rgb(15 23 42) !important;
    font-weight: 400 !important;
  }

  /* Card text improvements */
  .card-title {
    color: rgb(15 23 42) !important;
    font-weight: 600 !important;
  }

  /* Override Tailwind text colors for better readability */
  .text-sm {
    color: rgb(30 41 59) !important;
    font-weight: 500 !important;
  }

  .text-xs {
    color: rgb(30 41 59) !important;
    font-weight: 500 !important;
  }

  .text-lg {
    color: rgb(15 23 42) !important;
    font-weight: 600 !important;
  }

  .text-xl {
    color: rgb(15 23 42) !important;
    font-weight: 600 !important;
  }

  .text-2xl {
    color: rgb(15 23 42) !important;
    font-weight: 700 !important;
  }

  .text-3xl {
    color: rgb(15 23 42) !important;
    font-weight: 800 !important;
  }

  /* Specific overrides for card content */
  [class*="card"] p {
    color: rgb(30 41 59) !important;
    font-weight: 500 !important;
  }

  [class*="card"] h3 {
    color: rgb(15 23 42) !important;
    font-weight: 700 !important;
  }

  /* Button text improvements */
  button {
    font-weight: 600 !important;
  }

  /* Force dark text on all card elements */
  .card * {
    color: rgb(15 23 42) !important;
  }

  .card .text-muted-foreground {
    color: rgb(30 41 59) !important;
  }

  /* Navigation text */
  nav * {
    color: rgb(15 23 42) !important;
  }

  /* Ensure white text stays white on colored backgrounds */
  .text-white,
  .gradient-primary *,
  [class*="bg-blue"] .text-white,
  [class*="bg-primary"] * {
    color: rgb(255 255 255) !important;
  }
}
