
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Invoice App Design System */
@layer base {
  :root {
    --background: 248 250 252;
    --foreground: 15 23 42;

    --card: 255 255 255;
    --card-foreground: 15 23 42;

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 51 65 85;

    --muted: 241 245 249;
    --muted-foreground: 100 116 139;

    --accent: 59 130 246;
    --accent-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 51 65 85;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 241 245 249;
    --sidebar-accent-foreground: 51 65 85;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 59 130 246;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;

    --card: 30 41 59;
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 51 65 85;
    --secondary-foreground: 203 213 225;

    --muted: 51 65 85;
    --muted-foreground: 148 163 184;

    --accent: 59 130 246;
    --accent-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 59 130 246;

    --sidebar-background: 30 41 59;
    --sidebar-foreground: 203 213 225;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 51 65 85;
    --sidebar-accent-foreground: 203 213 225;
    --sidebar-border: 51 65 85;
    --sidebar-ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  ::selection {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}
 
@layer components {
  .gradient-primary {
    @apply bg-gradient-to-br from-invoice-blue-500 to-invoice-lightblue-500;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-br from-invoice-blue-50 to-invoice-lightblue-50;
  }
  
  .glass-effect {
    @apply bg-white/10 backdrop-blur-sm border border-white/20;
  }
  
  .invoice-shadow {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
  }
}
