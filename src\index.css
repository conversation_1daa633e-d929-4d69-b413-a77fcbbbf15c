
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Invoice App Design System */
@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 15 23 42;

    --card: 255 255 255;
    --card-foreground: 15 23 42;

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 248 250 252;
    --secondary-foreground: 30 41 59;

    --muted: 248 250 252;
    --muted-foreground: 71 85 105;

    --accent: 59 130 246;
    --accent-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 30 41 59;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 248 250 252;
    --sidebar-accent-foreground: 30 41 59;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 59 130 246;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;

    --card: 30 41 59;
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 51 65 85;
    --secondary-foreground: 203 213 225;

    --muted: 51 65 85;
    --muted-foreground: 148 163 184;

    --accent: 59 130 246;
    --accent-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 59 130 246;

    --sidebar-background: 30 41 59;
    --sidebar-foreground: 203 213 225;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 51 65 85;
    --sidebar-accent-foreground: 203 213 225;
    --sidebar-border: 51 65 85;
    --sidebar-ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-size: 16px;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground font-semibold;
    line-height: 1.3;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  p {
    @apply text-foreground;
    line-height: 1.7;
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground)) !important;
    font-weight: 500;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1.4;
    font-weight: 500;
  }

  ::selection {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}
 
@layer components {
  .gradient-primary {
    @apply bg-gradient-to-br from-invoice-blue-500 to-invoice-lightblue-500;
  }

  .gradient-secondary {
    @apply bg-gradient-to-br from-invoice-blue-50 to-invoice-lightblue-50;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-sm border border-white/20;
  }

  .invoice-shadow {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
  }

  /* Enhanced readability utilities */
  .text-readable {
    @apply text-foreground;
    font-weight: 500;
    letter-spacing: 0.01em;
  }

  .text-readable-muted {
    @apply text-muted-foreground;
    font-weight: 500;
    letter-spacing: 0.01em;
  }

  .card-title-readable {
    @apply text-foreground font-semibold;
    font-size: 1.125rem;
    line-height: 1.4;
    letter-spacing: -0.01em;
  }

  .button-text-readable {
    font-weight: 600;
    letter-spacing: 0.01em;
  }

  /* Improved contrast for stats and metrics */
  .stat-number {
    @apply text-foreground;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  .stat-label {
    @apply text-muted-foreground;
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: 0.01em;
  }
}
